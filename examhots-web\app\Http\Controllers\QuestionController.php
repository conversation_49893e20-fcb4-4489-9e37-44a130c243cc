<?php

namespace App\Http\Controllers;

use App\Models\Answer;
use App\Models\Question;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

use App\Models\QuestionMaterial;

class QuestionController extends Controller
{
    /**
     * Sanitize HTML content by removing all HTML tags
     */
    private function sanitizeHtml($content)
    {
        if (empty($content)) {
            return $content;
        }

        // Remove all HTML tags and decode HTML entities
        $sanitized = strip_tags($content);
        $sanitized = html_entity_decode($sanitized, ENT_QUOTES, 'UTF-8');

        // Trim whitespace
        return trim($sanitized);
    }

    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $user = Auth::user();

        // Admin can see all question materials, teachers can only see their own
        if ($user->role === 'admin') {
            $question = QuestionMaterial::with('teacher')->orderBy('name', 'asc')->get();
        } else {
            $question = QuestionMaterial::where('teacher_id', $user->id)->orderBy('name', 'asc')->get();
        }

        return view('admin.exam.questionbank.index', [
            'title' => 'Bank Soal',
            'question' => $question,
        ]);
    }

    public function add()
    {
        if (!Auth::check()) {
            return response('Unauthorized', 401);
        }

        $user = Auth::user();
        $teachers = [];

        // If admin, get list of teachers for selection
        if ($user->role === 'admin') {
            $teachers = User::where('role', 'teacher')->select('id', 'name', 'email')->orderBy('name', 'asc')->get();
        }

        return view('admin.exam.questionbank.add', [
            'teachers' => $teachers,
            'isAdmin' => $user->role === 'admin'
        ]);
    }

    public function processAdd(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            $user = Auth::user();

            $validationRules = [
                'name' => 'required',
                'description' => 'required|max:200',
            ];

            $validationMessages = [
                'name.required' => 'Nama bank soal wajib diisi',
                'description.required' => 'Deskripsi bank soal wajib diisi',
                'description.max' => 'Deskripsi bank soal tidak boleh lebih dari 200 karakter',
            ];

            // If admin, require teacher selection
            if ($user->role === 'admin') {
                $validationRules['teacher_id'] = 'required|exists:users,id';
                $validationMessages['teacher_id.required'] = 'Pilih guru untuk bank soal ini';
                $validationMessages['teacher_id.exists'] = 'Guru yang dipilih tidak valid';
            }

            $request->validate($validationRules, $validationMessages);

            QuestionMaterial::create([
                'name' => $request->name,
                'description' => $request->description,
                'teacher_id' => $user->role === 'admin' ? $request->teacher_id : $user->id,
            ]);

            return redirect()->route('question.index')->with('success', 'Data bank soal berhasil ditambahkan');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            return redirect()->route('question.index')->with('error', 'Gagal menambahkan data bank soal: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::find($id);

        if (!$question) {
            return redirect()->route('question.index')
                ->with('error', 'Bank soal tidak ditemukan.');
        }

        $user = Auth::user();
        $teachers = [];

        // If admin, get list of teachers for selection
        if ($user->role === 'admin') {
            $teachers = User::where('role', 'teacher')->select('id', 'name', 'email')->orderBy('name', 'asc')->get();
        }

        return view('admin.exam.questionbank.edit', [
            'title' => 'Edit Bank Soal',
            'question' => $question,
            'teachers' => $teachers,
            'isAdmin' => $user->role === 'admin'
        ]);
    }

    public function processEdit(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            $questionMaterial = QuestionMaterial::find($id);

            if (!$questionMaterial) {
                return redirect()->route('question.index')->with('error', 'Bank soal tidak ditemukan.');
            }

            $user = Auth::user();

            $validationRules = [
                'name' => 'required',
                'description' => 'required|max:200',
            ];

            $validationMessages = [
                'name.required' => 'Nama bank soal wajib diisi',
                'description.required' => 'Deskripsi bank soal wajib diisi',
                'description.max' => 'Deskripsi bank soal tidak boleh lebih dari 200 karakter',
            ];

            // If admin, require teacher selection
            if ($user->role === 'admin') {
                $validationRules['teacher_id'] = 'required|exists:users,id';
                $validationMessages['teacher_id.required'] = 'Pilih guru untuk bank soal ini';
                $validationMessages['teacher_id.exists'] = 'Guru yang dipilih tidak valid';
            }

            $request->validate($validationRules, $validationMessages);

            $updateData = [
                'name' => $request->name,
                'description' => $request->description,
            ];

            // Only update teacher_id if admin
            if ($user->role === 'admin') {
                $updateData['teacher_id'] = $request->teacher_id;
            }

            QuestionMaterial::where('id', $id)->update($updateData);

            return redirect()->route('question.index')->with('success', 'Data bank soal berhasil diubah');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali data yang dimasukkan.');
        } catch (\Exception $e) {
            return redirect()->route('question.index')->with('error', 'Gagal mengubah data bank soal: ' . $e->getMessage());
        }
    }

    public function delete($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        // $id adalah ID dari QuestionMaterial (bank soal), bukan Question
        $questionMaterial = QuestionMaterial::find($id);

        if (!$questionMaterial) {
            return redirect()->route('question.index')
                ->with('error', 'Bank soal tidak ditemukan.');
        }

        // Ambil semua soal yang terkait dengan bank soal ini
        $questions = Question::where('questionmaterialid', $id)->get();

        // Hapus semua gambar yang terkait dengan soal-soal
        foreach ($questions as $question) {
            if (!empty($question->img)) {
                $images = explode(',', $question->img);
                foreach ($images as $img) {
                    $filePath = public_path('storage/uploads/images/question/' . $img);
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }
            }
        }

        // Hapus semua jawaban untuk semua soal dalam bank soal ini
        $questionIds = $questions->pluck('id');
        Answer::whereIn('questionid', $questionIds)->delete();

        // Hapus semua soal yang terkait dengan bank soal
        Question::where('questionmaterialid', $id)->delete();

        // Hapus bank soal itu sendiri
        $questionMaterial->delete();

        return redirect()->route('question.index')
            ->with('success', 'Bank soal beserta semua soal dan jawaban berhasil dihapus.');
    }

    public function detail($materialid)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $questionmaterial = QuestionMaterial::find($materialid);

        if (!$questionmaterial) {
            return redirect()->route('question.index')
                ->with('error', 'Bank soal tidak ditemukan.');
        }

        $questions = Question::with('answers')
            ->where('questionmaterialid', $materialid)
            ->orderBy('id', 'asc')
            ->get();

        return view('admin.exam.questionbank.detail', [
            'title' => 'Detail Bank Soal',
            'questionmaterial' => $questionmaterial,
            'questions' => $questions,
            'pg_questions' => $questions->where('type', 'pilihan_ganda'),
            'urai_questions' => $questions->where('type', 'uraian_singkat'),
            'esai_questions' => $questions->where('type', 'esai'),
        ]);
    }

    public function addDetail(Request $request, $materialid)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::findOrFail($materialid);

        return view('admin.exam.questionbank.form.choose', compact('question') + [
            'title' => 'Form Pilih Soal',
        ]);
    }

    public function chooseDetailType(Request $request, $materialid)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated.'
            ], 401);
        }

        $request->validate([
            'type' => 'required|in:pilihan_ganda,uraian_singkat,esai',
        ]);

        $type = $request->input('type');
        $question = QuestionMaterial::findOrFail($materialid);

        $view = match ($type) {
            'pilihan_ganda' => 'admin.exam.questionbank.form.form_pilihan_ganda',
            'uraian_singkat' => 'admin.exam.questionbank.form.form_uraian_singkat',
            'esai' => 'admin.exam.questionbank.form.form_esai',
        };

        $html = view($view, [
            'title' => 'Buat Soal ' . ucwords(str_replace('_', ' ', $type)),
            'material' => $question,
            'type' => $type,
            'mode' => 'create',
        ])->render();

        return response()->json([
            'success' => true,
            'html' => $html
        ]);
    }

    public function addDetailType($materialid, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $allowedTypes = ['pilihan_ganda', 'uraian_singkat', 'esai'];
        if (!in_array($type, $allowedTypes)) {
            abort(404, 'Tipe soal tidak dikenali');
        }

        $question = QuestionMaterial::findOrFail($materialid);

        $view = match ($type) {
            'pilihan_ganda' => 'admin.exam.questionbank.form.form_pilihan_ganda',
            'uraian_singkat' => 'admin.exam.questionbank.form.form_uraian_singkat',
            'esai' => 'admin.exam.questionbank.form.form_esai',
        };

        return view($view, [
            'title' => 'Buat Soal ' . ucwords(str_replace('_', ' ', $type)),
            'material' => $question,
            'type' => $type,
            'mode' => 'create',
        ]);
    }

    public function processAddDetailType(Request $request, $materialid, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            if ($type === 'pilihan_ganda') {
                $request->validate([
                    'question' => 'required|string',
                    'answer_1' => 'required|string',
                    'answer_2' => 'required|string',
                    'answer_3' => 'required|string',
                    'answer_4' => 'required|string',
                    'answer_5' => 'required|string',
                    'correct' => 'required|in:A,B,C,D,E',
                ], [
                    'question.required' => 'Pertanyaan wajib diisi',
                    'answer_1.required' => 'Jawaban A wajib diisi',
                    'answer_2.required' => 'Jawaban B wajib diisi',
                    'answer_3.required' => 'Jawaban C wajib diisi',
                    'answer_4.required' => 'Jawaban D wajib diisi',
                    'answer_5.required' => 'Jawaban E wajib diisi',
                    'correct.required' => 'Jawaban yang benar wajib dipilih',
                    'correct.in' => 'Jawaban yang benar harus salah satu dari A, B, C, D, atau E',
                ]);
            } else if ($type === 'uraian_singkat') {
                // Custom validation for whitespace-only content
                $request->validate([
                    'question' => [
                        'required',
                        'string',
                        function ($attribute, $value, $fail) {
                            if (trim(strip_tags($value)) === '') {
                                $fail('Pertanyaan tidak boleh kosong atau hanya berisi spasi.');
                            }
                        },
                    ],
                    'answer' => [
                        'required',
                        'string',
                        function ($attribute, $value, $fail) {
                            if (trim(strip_tags($value)) === '') {
                                $fail('Jawaban tidak boleh kosong atau hanya berisi spasi.');
                            }
                        },
                    ],
                ], [
                    'question.required' => 'Pertanyaan wajib diisi',
                    'answer.required' => 'Jawaban wajib diisi',
                ]);
            } else if ($type === 'esai') {
                // Custom validation for whitespace-only content
                $request->validate([
                    'question' => [
                        'required',
                        'string',
                        function ($attribute, $value, $fail) {
                            if (trim(strip_tags($value)) === '') {
                                $fail('Pertanyaan tidak boleh kosong atau hanya berisi spasi.');
                            }
                        },
                    ],
                    'answer' => [
                        'required',
                        'string',
                        function ($attribute, $value, $fail) {
                            if (trim(strip_tags($value)) === '') {
                                $fail('Kunci jawaban tidak boleh kosong atau hanya berisi spasi.');
                            }
                        },
                    ],
                    'score' => 'required|numeric',
                ], [
                    'question.required' => 'Pertanyaan wajib diisi',
                    'answer.required' => 'Kunci jawaban wajib diisi',
                    'score.required' => 'Skor wajib diisi',
                    'score.numeric' => 'Skor harus berupa angka',
                ]);
            } else {
                abort(400, 'Tipe soal tidak dikenali.');
            }

            $question = new Question();
            $question->questionmaterialid = $materialid;
            $question->question = $this->sanitizeHtml($request->input('question'));
            $question->type = $type;
            $question->img = $request->input('uploaded_images');
            $question->save();

            if ($type === 'pilihan_ganda') {
                $opsi = [
                    'A' => $request->input('answer_1'),
                    'B' => $request->input('answer_2'),
                    'C' => $request->input('answer_3'),
                    'D' => $request->input('answer_4'),
                    'E' => $request->input('answer_5'),
                ];

                foreach ($opsi as $label => $jawaban) {
                    if (trim($jawaban) === '') continue;

                    $answer =  new Answer();
                    $answer->questionid = $question->id;
                    $answer->answer = $this->sanitizeHtml($jawaban);
                    $answer->is_correct = $label === $request->input('correct');
                    $answer->save();
                }
            } else if ($type === 'uraian_singkat') {
                $answer =  new Answer();
                $answer->questionid = $question->id;
                $answer->answer = $this->sanitizeHtml($request->input('answer'));
                $answer->is_correct = 1;
                $answer->save();
            } else if ($type === 'esai') {
                $answer =  new Answer();
                $answer->questionid = $question->id;
                $answer->answer = $this->sanitizeHtml($request->input('answer'));
                $answer->is_correct = 1;
                $answer->score = intval($request->input('score', 0));
                $answer->save();
            }

            return redirect()->route('question.detail', $materialid)
                ->with('success', 'Soal berhasil disimpan.');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            return redirect()->route('question.detail', $materialid)
                ->with('error', 'Gagal menyimpan soal: ' . $e->getMessage());
        }
    }

    public function editDetailType($questionid)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = Question::with('answers')->findOrFail($questionid);
        $type = $question->type;

        $view = match ($type) {
            'pilihan_ganda' => 'admin.exam.questionbank.form.form_pilihan_ganda',
            'uraian_singkat' => 'admin.exam.questionbank.form.form_uraian_singkat',
            'esai' => 'admin.exam.questionbank.form.form_esai',
        };

        $html = view($view, [
            'title' => 'Edit Soal ' . ucwords(str_replace('_', ' ', $type)),
            'question' => $question,
            'type' => $type,
            'mode' => 'edit',
        ])->render();

        return response()->json([
            'success' => true,
            'html' => $html
        ]);
    }

    public function processEditDetailType(Request $request, $questionid, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = Question::with('answers')->findOrFail($questionid);

        if ($type === 'pilihan_ganda') {
            $request->validate([
                'question' => 'required|string',
                'correct' => 'required|in:A,B,C,D,E',
                'answer_1' => 'required|string',
                'answer_2' => 'required|string',
                'answer_3' => 'required|string',
                'answer_4' => 'required|string',
                'answer_5' => 'required|string',
            ]);
        } else if ($type === 'uraian_singkat') {
            // Custom validation for whitespace-only content
            $request->validate([
                'question' => [
                    'required',
                    'string',
                    function ($attribute, $value, $fail) {
                        if (trim(strip_tags($value)) === '') {
                            $fail('Pertanyaan tidak boleh kosong atau hanya berisi spasi.');
                        }
                    },
                ],
                'answer' => [
                    'required',
                    'string',
                    function ($attribute, $value, $fail) {
                        if (trim(strip_tags($value)) === '') {
                            $fail('Jawaban tidak boleh kosong atau hanya berisi spasi.');
                        }
                    },
                ],
            ]);
        } else if ($type === 'esai') {
            // Custom validation for whitespace-only content
            $request->validate([
                'question' => [
                    'required',
                    'string',
                    function ($attribute, $value, $fail) {
                        if (trim(strip_tags($value)) === '') {
                            $fail('Pertanyaan tidak boleh kosong atau hanya berisi spasi.');
                        }
                    },
                ],
                'answer' => [
                    'required',
                    'string',
                    function ($attribute, $value, $fail) {
                        if (trim(strip_tags($value)) === '') {
                            $fail('Kunci jawaban tidak boleh kosong atau hanya berisi spasi.');
                        }
                    },
                ],
                'score' => 'required|numeric',
            ]);
        } else {
            abort(400, 'Tipe soal tidak dikenali.');
        }

        // Update soal utama
        $question->question = $this->sanitizeHtml($request->input('question'));
        if ($request->filled('uploaded_images')) {
            $newImages = explode(',', $request->input('uploaded_images'));
            $oldImages = [];

            if (!empty($question->img)) {
                $oldImages = explode(',', $question->img);
                // Hapus yang sudah tidak dipakai
                foreach ($oldImages as $img) {
                    if (!in_array($img, $newImages)) {
                        $filePath = public_path('storage/uploads/images/question/' . $img);
                        if (file_exists($filePath)) {
                            unlink($filePath);
                        }
                    }
                }
            }
            $question->img = implode(',', $newImages);
        } else {
            // Tidak ada gambar sama sekali, hapus semua gambar lama
            if (!empty($question->img)) {
                $oldImages = explode(',', $question->img);
                foreach ($oldImages as $img) {
                    $filePath = public_path('storage/uploads/images/question/' . $img);
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }
            }
            $question->img = null;
        }

        $question->save();

        if ($type === 'pilihan_ganda') {
            $answers = $question->answers->values();
            $labels = ['A', 'B', 'C', 'D', 'E'];

            // Update jawaban satu-satu sesuai label
            foreach ($labels as $i => $label) {
                $answerInput = $request->input('answer_' . ($i + 1));
                $isCorrect = $request->input('correct') === $label;

                if (isset($answers[$i])) {
                    $answers[$i]->update([
                        'answer' => $this->sanitizeHtml($answerInput),
                        'is_correct' => $isCorrect,
                    ]);
                }
            }
        } else if ($type === 'uraian_singkat') {
            $answer = $question->answers->first();
            if ($answer) {
                $answer->update([
                    'answer' => $this->sanitizeHtml($request->input('answer')),
                    'is_correct' => true,
                ]);
            }
        } else if ($type === 'esai') {
            $answer = $question->answers->first();
            if ($answer) {
                $answer->update([
                    'answer' => $this->sanitizeHtml($request->input('answer')),
                    'is_correct' => true,
                    'score' => $request->input('score'),
                ]);
            }
        }

        return redirect()->route('question.detail', $question->questionmaterialid)
            ->with('success', 'Soal berhasil diperbarui!');
    }

    public function updateMaterialScores($materialid, Request $request)
    {
        $request->validate([
            'pg_total_score' => 'nullable|integer|min:0',
            'uraian_total_score' => 'nullable|integer|min:0',
        ]);

        $material = QuestionMaterial::findOrFail($materialid);

        $material->update([
            'pg_total_score' => $request->input('pg_total_score'),
            'uraian_total_score' => $request->input('uraian_total_score'),
        ]);

        // Check if request expects JSON (AJAX)
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Pengaturan skor berhasil diperbarui!',
                'data' => $material
            ]);
        }

        return redirect()->route('question.detail', $materialid)
            ->with('success', 'Pengaturan skor berhasil diperbarui!');
    }

    public function deleteDetail($questionid)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = Question::find($questionid);

        if (!$question) {
            return redirect()->route('question.index')
                ->with('error', 'Soal tidak ditemukan.');
        }

        $materialid = $question->questionmaterialid;

        if (!empty($question->img)) {
            $images = explode(',', $question->img);
            foreach ($images as $img) {
                $filePath = public_path('storage/uploads/images/question/' . $img);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
        }

        $question->answers()->delete();
        $question->delete();

        return redirect()->route('question.detail', $materialid)
            ->with('success', 'Soal berhasil dihapus.');
    }

    public function getQuestion($id, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $questions = Question::select('question.*', 'answer.*')
            ->join('answer', 'answer.questionid', '=', 'question.id') // Ganti sesuai relasi sebenarnya
            ->where('question.questionmaterialid', $id) // Sesuaikan nama kolomnya
            ->where('question.type', $type)
            ->get();

        if ($type === 'pilihan_ganda') {
            return view('admin.questionbank.question.components.pilihan_ganda', compact('questions'));
        } elseif ($type === 'uraian_singkat') {
            return view('admin.questionbank.question.components.uraian_singkat', compact('questions'));
        } elseif ($type === 'esai') {
            return view('admin.questionbank.question.components.esai', compact('questions'));
        } else {
            abort(404, 'Tipe soal tidak dikenali.');
        }
    }

    public function upload(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $request->validate([
                'file' => 'required|image|mimes:jpeg,jpg,png,webp|max:2048',
            ]);

            $file = $request->file('file');

            if (!$file) {
                return response()->json(['error' => 'No file uploaded'], 400);
            }

            $filename = hash('sha256', time() . '_' . uniqid()) . '.' . $file->getClientOriginalExtension();

            // Alternative approach without using Storage facade to avoid finfo dependency
            $uploadPath = public_path('storage/uploads/images/question/');

            // Create directory if it doesn't exist
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Move uploaded file directly
            $file->move($uploadPath, $filename);

            return response()->json(['filename' => $filename]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Upload failed: ' . $e->getMessage()], 500);
        }
    }
}
